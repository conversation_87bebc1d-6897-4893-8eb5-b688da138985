<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Transcript</title>
    <style>
        body {
            background-color: #e0e0e0; /* Light gray background for contrast with the "page" */
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px 0; /* Add some padding to top/bottom of viewport */
            font-size: 10px;
            line-height: 1.3;
        }
        .a4-container {
            background-color: #fff;
            width: 210mm; /* A4 width */
            min-height: 270mm; /* Approximate A4 height, content will push it */
            margin: 20px auto;
            padding: 15mm; /* Page margins */
            box-sizing: border-box;
            box-shadow: 0 0 15px rgba(0,0,0,0.2);
            overflow-x: auto; /* Prevent content from breaking layout too easily */
            position: relative;
            background-image: url('./OIP_lightened_background.png'), url('OIP_lightened_background.png');
            background-repeat: no-repeat;
            background-position: center;
            background-size: 400px 400px;
        }
        
        /* Background watermark */
        .a4-container::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 600px;
            height: 600px;
            background-image: url('./OIP_lightened_background.png'), url('OIP_lightened_background.png');
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
            opacity: 0.25;
            z-index: 1;
            pointer-events: none;
        }
        
        /* Ensure content appears above watermark */
        .a4-container > * {
            position: relative;
            z-index: 2;
        }
        
        .header {
            text-align: center;
            margin-bottom: 15px;
        }
        .header h1 {
            font-size: 18px;
            margin: 0 0 2px 0;
            font-weight: bold;
        }
        .header p {
            font-size: 12px;
            margin: 2px 0;
            font-weight: bold;
        }

        .info-grid {
            display: grid;
            /*grid-template-columns: 1fr 1fr;*/ /* Original user value */
            grid-template-columns: auto 1fr; /* MODIFIED: Left column auto-sizes, right takes rest */
            /*gap: 10px;*/ /* Original user value */
            gap: 20px; /* MODIFIED: Adjust this gap to control space between left info and right tables */
            margin-bottom: 10px;
        }

        /* Applied to p tags directly in student-info-left and the new detail-line divs */
        .student-info-left p,
        .student-info-left .detail-line {
            margin: 2px 0;
            font-size: 10px;
        }

        /* Applies to all strong tags within student-info-left for label styling */
        .student-info-left strong {
            display: inline-block;
            min-width: 80px; /* 增加宽度确保所有标签对齐 */
            font-weight: bold; /* Explicitly set bold, though strong usually is */
        }
        /* Override for James Bond's name so it doesn't get min-width */
        .student-info-left > p:first-child > strong {
            min-width: auto;
        }

        /* New CSS for aligning SN/Grade and SSID/Gender lines */
        .student-info-left .detail-line {
            display: flex; /* Use flexbox to position items on the same line */
            /* margin and font-size are covered by the rule above */
        }

        .student-info-left .detail-line .field-group-1 {
            width: 150px; /* 增加宽度确保第一组字段有足够空间 */
                          /* Adjust this value to control the starting position of Grade/Gender */
            display: inline-block; /* Allow width to apply */
        }

        .student-info-left .detail-line .field-group-2 {
            display: inline-block; /* For the Grade/Gender part */
        }

        /* 确保第二组中的标签也对齐 */
        .student-info-left .detail-line .field-group-2 strong {
            min-width: 60px; /* Grade: 和 Gender: 标签对齐 */
        }


        /* Styles for student-info-right and its contents */
        .student-info-right {
            /* This div now only contains the summary-tables-container */
        }

        .summary-tables-container {
            display: flex; /* Remains flex, though only one child table now */
            flex-direction: column;
            gap: 5px;
        }
        .summary-tables-container table {
            border-collapse: collapse;
            font-size: 9px;
            width: auto;
        }
        .summary-tables-container th, .summary-tables-container td {
            border: 1px solid black;
            padding: 3px 4px;
            text-align: left;
            vertical-align: top; /* Consider 'middle' if content looks better centered vertically */
        }
        .summary-tables-container th {
            background-color: #cccccc;
            font-weight: bold;
            /* text-align: center; was handled by class, ensure consistency */
        }
        .right-align { text-align: right; }
        .center-align { text-align: center; }

        .courses-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            font-size: 9.5px;
        }
        .courses-table th, .courses-table td {
            border: 1px solid black;
            padding: 3px 4px;
            text-align: left;
            vertical-align: top;
        }
        .courses-table > thead > tr > th {
            background-color: #b0b0b0;
            font-weight: bold;
            text-align: center;
        }
        .courses-section-header td {
            font-weight: bold;
            text-align: left !important;
            background-color: #e0e0e0 !important;
            padding-left: 5px !important;
        }
        .course-credits { text-align: right; }
        .course-grades { text-align: center; min-width: 18px; }

        .gpa-summary {
            font-size: 9.5px;
            margin-bottom: 10px;
            line-height: 1.5;
        }
        .gpa-summary span {
            margin-right: 10px;
            display: inline-block;
        }
        .gpa-summary .gpa-group {
            margin-right: 20px;
        }

        .footer-info p {
            font-size: 9px;
            margin-bottom: 10px;
            line-height: 1.2;
        }
        .footer-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            align-items: end;
            margin-bottom: 20px;
            font-size: 9.5px;
        }
        .footer-grid div {
            padding: 0 5px;
        }
        .footer-grid .center-text { text-align: center; font-style: italic; }
        .footer-grid .right-text { text-align: right; }

        .signature-line {
            display: block;
            margin-top: 25px;
            margin-bottom: 15px;
            font-size: 9.5px;
            text-align: center;
            position: relative;
        }
        .signature-line div {
            border-top: 1px solid black;
            padding-top: 4px;
            text-align: center;
            width: 200px;
            margin: 0 auto;
            position: relative;
            min-height: 50px;
        }

        /* Hand-written signature style */
        .signature-text {
            position: absolute;
            top: -45px;
            left: 50%;
            transform: translateX(-50%);
            width: 250px;
            height: 60px;
            background-image: url('屏幕截图 2025-08-02 180314.png');
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
            text-indent: -9999px;
            text-align: center;
            font-size: 16px;
            font-weight: normal;
            line-height: 50px;
            font-family: "Brush Script MT", "Lucida Handwriting", "Segoe Script", cursive;
            font-style: italic;
            color: #2c3e50;
        }
        .date-footer {
            text-align: right;
            font-size: 9.5px;
        }

        /* 可编辑输入框样式 */
        .editable-input {
            border: none;
            background: transparent;
            font-family: inherit;
            font-size: inherit;
            font-weight: inherit;
            padding: 0;
            margin: 0;
            outline: none;
        }
        .editable-input:focus {
            background: #f0f8ff;
            outline: 1px solid #007cba;
        }

        /* 打印专用样式 */
        @media print {
            body {
                background-color: white !important;
                padding: 0 !important;
                margin: 0 !important;
                font-size: 10px !important;
                line-height: 1.3 !important;
            }

            .a4-container {
                width: 210mm !important;
                min-height: 270mm !important;
                margin: 0 auto !important;
                padding: 15mm !important;
                box-shadow: none !important;
                page-break-inside: avoid !important;
                transform: none !important;
                overflow: visible !important;
                background-color: #fff !important;
                background-image: url('./OIP_lightened_background.png'), url('OIP_lightened_background.png') !important;
                background-repeat: no-repeat !important;
                background-position: center !important;
                background-size: 400px 400px !important;
            }

            /* 保持背景水印 */
            .a4-container::before {
                content: '' !important;
                position: absolute !important;
                top: 50% !important;
                left: 50% !important;
                transform: translate(-50%, -50%) !important;
                width: 600px !important;
                height: 600px !important;
                background-image: url('./OIP_lightened_background.png'), url('OIP_lightened_background.png') !important;
                background-repeat: no-repeat !important;
                background-position: center !important;
                background-size: contain !important;
                opacity: 0.25 !important;
                z-index: 1 !important;
                pointer-events: none !important;
            }

            /* 确保内容在水印上方 */
            .a4-container > * {
                position: relative !important;
                z-index: 2 !important;
            }

            .edit-controls {
                display: none !important;
            }

            .header h1 {
                font-size: 18px !important;
                margin: 0 0 2px 0 !important;
            }

            .header p {
                font-size: 12px !important;
                margin: 2px 0 !important;
            }

            .info-grid {
                margin-bottom: 15px !important;
                gap: 20px !important;
            }

            .student-info-left p,
            .student-info-left .detail-line {
                margin: 2px 0 !important;
                font-size: 10px !important;
            }

            .courses-table {
                font-size: 9.5px !important;
                margin-bottom: 10px !important;
            }

            .courses-table th, .courses-table td {
                padding: 3px 4px !important;
            }

            .summary-tables-container table {
                font-size: 9px !important;
            }

            .summary-tables-container th, .summary-tables-container td {
                padding: 3px 4px !important;
            }

            .gpa-summary {
                font-size: 9.5px !important;
                margin-bottom: 10px !important;
                line-height: 1.5 !important;
            }

            .footer-info p {
                font-size: 9px !important;
                margin-bottom: 10px !important;
            }

            .footer-grid {
                margin-bottom: 20px !important;
                font-size: 9.5px !important;
            }

            .signature-line {
                margin-top: 25px !important;
                margin-bottom: 15px !important;
                font-size: 9.5px !important;
                position: relative !important;
            }

            .signature-line div {
                min-height: 50px !important;
                position: relative !important;
                padding-top: 4px !important;
                border-top: 1px solid black !important;
            }

            .signature-text {
                font-size: 16px !important;
                position: absolute !important;
                top: -45px !important;
                left: 50% !important;
                transform: translateX(-50%) !important;
                width: 250px !important;
                text-align: center !important;
                height: 60px !important;
                line-height: 50px !important;
                background-image: url('屏幕截图 2025-08-02 180314.png') !important;
                background-repeat: no-repeat !important;
                background-position: center !important;
                background-size: contain !important;
                text-indent: -9999px !important;
            }

            .date-footer {
                font-size: 9.5px !important;
            }

            /* 强制单页显示 */
            @page {
                size: A4;
                margin: 0.5in;
            }

            /* 强制打印背景 */
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>
</head>
<body>
    <!-- 编辑控制按钮 -->
    <div class="edit-controls">
        <button class="edit-btn" onclick="toggleEdit()">开启编辑</button>
        <button class="edit-btn save" onclick="saveData()">保存数据</button>
        <button class="edit-btn" onclick="loadData()">加载数据</button>
        <button class="edit-btn" onclick="saveAsImage()" style="background:#FF9800;">保存图片</button>
        <button class="edit-btn" onclick="printTranscript()" style="background:#9C27B0;">打印</button>
    </div>
    <div class="a4-container">
        <div class="header">
            <h1>Student Transcript</h1>
            <p>West Anchorage High School</p>
        </div>

        <div class="info-grid">
            <div class="student-info-left">
                <p><strong><input type="text" class="editable-input" value="Dicki Jerde" data-field="studentName"></strong></p>
                <p><input type="text" class="editable-input" value="12061 Johns Road" data-field="address1"></p>
                <p><input type="text" class="editable-input" value="Anchorage, Alaska 99515" data-field="address2"></p>
                <br>
                <div class="detail-line">
                    <span class="field-group-1"><strong>SN:</strong> <input type="text" class="editable-input" value="247938" data-field="sn" style="width:60px;"></span>
                    <span class="field-group-2"><strong>Grade:</strong> <input type="text" class="editable-input" value="12" data-field="grade" style="width:30px;"></span>
                </div>
                <div class="detail-line">
                    <span class="field-group-1"><strong>SSID:</strong> <input type="text" class="editable-input" value="892547" data-field="ssid" style="width:60px;"></span>
                    <span class="field-group-2"><strong>Gender:</strong> <input type="text" class="editable-input" value="M" data-field="gender" style="width:20px;"></span>
                </div>
                <p><strong>Birthdate:</strong> <input type="text" class="editable-input" value="03/22/2006" data-field="birthdate" style="width:80px;"></p>
                <p><strong>Grad. Date:</strong> <input type="text" class="editable-input" value="07/2025" data-field="gradDate" style="width:60px;"></p>
                <p><strong>Exit Date:</strong> <input type="text" class="editable-input" value="07/2025" data-field="exitDate" style="width:60px;"></p>
            </div>
            <div class="student-info-right">
                <div class="summary-tables-container">
                    <table class="summary-gpa"> <thead>
                            <tr>
                                <th></th> <th class="center-align">Credit</th>
                                <th class="center-align">GPA</th>
                                <th class="center-align">Testing</th>
                                <th class="center-align">ACT</th>
                                <th class="center-align">SAT</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Cumulative:</td>
                                <td class="right-align">29.375</td>
                                <td class="right-align">3.428</td>
                                <td>Test Date</td>
                                <td class="center-align">04/05/2025</td>
                                <td class="center-align">--</td>
                            </tr>
                            <tr>
                                <td>12th:</td>
                                <td class="right-align">4.750</td>
                                <td class="right-align">3.370</td>
                                <td>Composite</td>
                                <td class="center-align">31</td>
                                <td class="center-align">--</td>
                            </tr>
                            <tr>
                                <td>11th:</td>
                                <td class="right-align">8.875</td>
                                <td class="right-align">3.472</td>
                                <td>English (e)</td>
                                <td class="center-align">32</td>
                                <td class="center-align">--</td>
                            </tr>
                            <tr>
                                <td>10th:</td>
                                <td class="right-align">8.750</td>
                                <td class="right-align">3.316</td>
                                <td>Math (m)</td>
                                <td class="center-align">28</td>
                                <td class="center-align">--</td>
                            </tr>
                            <tr>
                                <td>9th:</td>
                                <td class="right-align">7.000</td>
                                <td class="right-align">3.314</td>
                                <td>Reading</td>
                                <td class="center-align">33</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>Class Rank:</td>
                                <td colspan="2" class="center-align">15 of 312</td>
                                <td>Science</td>
                                <td class="center-align">30</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td></td> <td></td>
                                <td></td>
                                <td>English Writing</td> <td class="center-align">--</td>
                                <td class="center-align">--</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    <table class="courses-table">
        <thead>
            <tr>
                <th>School/Course</th>
                <th>Credit</th>
                <th colspan="4">Grades</th>
                <th>School/Course</th>
                <th>Credit</th>
                <th colspan="4">Grades</th>
            </tr>
        </thead>
        <tbody>
            <tr class="courses-section-header">
                <td colspan="12">24-25 West Anchorage High School</td>
            </tr>
            <tr>
                <td>College English 1010</td>
                <td class="course-credits">0.33</td>
                <td class="course-grades">B+</td><td class="course-grades"></td><td class="course-grades"></td><td class="course-grades"></td>
                <td>Study Skills Chemistry Lab</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B+</td><td class="course-grades">B</td><td class="course-grades">B+</td><td class="course-grades">A-</td>
            </tr>
            <tr>
                <td>Biology 1010 Lab</td>
                <td class="course-credits">0.25</td>
                <td class="course-grades">B+</td><td></td><td></td><td></td>
                <td>Weight Training (9-12)</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A-</td><td class="course-grades">B+</td><td class="course-grades">A</td>
            </tr>
            <tr>
                <td>Design 1610 Screen Printing</td>
                <td class="course-credits">0.25</td>
                <td class="course-grades">B</td><td></td><td></td><td></td>
                <td>World Civ Honors</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A-</td><td class="course-grades">B+</td><td class="course-grades">A</td><td class="course-grades">A-</td>
            </tr>
            <tr>
                <td>Health 1000 Medical Terminology</td>
                <td class="course-credits">0.25</td>
                <td class="course-grades">B+</td><td></td><td></td><td></td>
                <td colspan="6"></td>
            </tr>
             <tr>
                <td>Study Skills AP Math Lab</td>
                <td class="course-credits">0.25</td>
                <td class="course-grades">B</td><td></td><td></td><td></td>
                <td colspan="6"></td>
            </tr>
            <tr class="courses-section-header">
                <td colspan="6">23-24 West Anchorage High School</td>
                <td colspan="6">20-21 West Anchorage High School</td>
            </tr>
            <tr>
                <td>AP Chemistry</td>
                <td class="course-credits">2.00</td>
                <td class="course-grades">B+</td><td class="course-grades">B</td><td class="course-grades">B-</td><td class="course-grades">B+</td>
                <td>Drivers Ed</td>
                <td class="course-credits">0.25</td>
                <td class="course-grades"></td><td class="course-grades"></td><td class="course-grades"></td><td class="course-grades">A</td>
            </tr>
            <tr>
                <td>AP English 11</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B+</td><td class="course-grades">A-</td><td class="course-grades">B</td><td class="course-grades">B+</td>
                <td>Biology Honors</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A-</td><td class="course-grades">B+</td><td class="course-grades">B</td><td class="course-grades">B+</td>
            </tr>
            <tr>
                <td>AP US History</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B</td><td class="course-grades">B+</td><td class="course-grades">B-</td><td class="course-grades">B+</td>
                <td>Computer Technology</td>
                <td class="course-credits">0.50</td>
                <td class="course-grades">A</td><td class="course-grades">A-</td><td class="course-grades">B+</td><td class="course-grades">A</td>
            </tr>
            <tr>
                <td>Art 1010</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B+</td><td class="course-grades">B</td><td class="course-grades">B-</td><td class="course-grades">B</td>
                <td>Construction Technology</td>
                <td class="course-credits">0.50</td>
                <td class="course-grades">A</td><td class="course-grades">B+</td><td class="course-grades">B</td><td class="course-grades">B+</td>
            </tr>
            <tr>
                <td>Guitar</td>
                <td class="course-credits">0.50</td>
                <td></td><td></td><td></td><td class="course-grades">A</td>
                <td>Geography for Life Honors</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B+</td><td class="course-grades">B</td><td class="course-grades">B+</td><td class="course-grades">A-</td>
            </tr>
            <tr>
                <td>Spanish I</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B+</td><td class="course-grades">B</td><td class="course-grades">B+</td><td class="course-grades">B</td>
                <td>Physical Skills</td>
                <td class="course-credits">0.50</td>
                <td class="course-grades">A</td><td class="course-grades">A-</td><td class="course-grades">B+</td><td class="course-grades">A</td>
            </tr>
            <tr>
                <td>Spanish II</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B</td><td class="course-grades">B-</td><td class="course-grades">B+</td><td class="course-grades">B</td>
                <td>Keyboarding & Communications Technology</td> <td class="course-credits">0.50</td>
                <td class="course-grades">A</td><td class="course-grades">A-</td><td class="course-grades">B+</td><td class="course-grades">A</td>
            </tr>
            <tr>
                <td>Study Skills Technology Lab</td>
                <td class="course-credits">0.50</td>
                <td class="course-grades">A</td><td></td><td></td><td></td>
                <td>Spanish I</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B+</td><td class="course-grades">B</td><td class="course-grades">B+</td><td class="course-grades">B</td>
            </tr>
            <tr>
                <td>Weight Training (9-12)</td>
                <td class="course-credits">0.50</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td></td><td></td>
                <td>Physical Skills</td>
                <td class="course-credits">0.50</td>
                <td class="course-grades">A</td><td class="course-grades">A-</td><td class="course-grades">B+</td><td class="course-grades">A</td>
            </tr>
            <tr class="courses-section-header">
                <td colspan="12">22-23 West Anchorage High School</td>
            </tr>
            <tr>
                <td>Computer Science 1100</td>
                <td class="course-credits">0.50</td>
                <td class="course-grades">B+</td><td class="course-grades">B</td><td></td><td></td>
                <td>Physics with Technology</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B</td><td class="course-grades">B+</td><td class="course-grades">B-</td><td class="course-grades">B</td>
            </tr>
            <tr>
                <td>Political Science 1100 American Government</td>
                <td class="course-credits">1.00</td>
                <td></td><td></td><td class="course-grades">B</td><td class="course-grades">B-</td>
                <td>Secondary Mathematics 3 Honors</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B</td><td class="course-grades">B-</td><td class="course-grades">C+</td><td class="course-grades">B</td>
            </tr>
            <tr>
                <td>US Gov. Center</td>
                <td class="course-credits">0.50</td>
                <td></td><td></td><td></td><td class="course-grades">A</td>
                <td>Secondary Mathematics 1 Honors</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B+</td><td class="course-grades">B</td><td class="course-grades">B-</td><td class="course-grades">B+</td>
            </tr>
            <tr class="courses-section-header">
                <td colspan="12">21-22 West Anchorage High School</td>
            </tr>
            <tr>
                <td>CAD Architectural Design 2</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B+</td><td class="course-grades">B</td><td class="course-grades">B-</td><td class="course-grades">B+</td>
                <td colspan="6"></td>
            </tr>
            <tr>
                <td>Ceramics I</td>
                <td class="course-credits">1.00</td>
                <td></td><td></td><td class="course-grades">A-</td><td class="course-grades">B+</td>
                <td colspan="6"></td>
            </tr>
            <tr>
                <td>Chemistry</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B</td><td class="course-grades">B-</td><td class="course-grades">B+</td><td class="course-grades">B</td>
                <td colspan="6"></td>
            </tr>
            <tr>
                <td>Digital Graphics Arts Intro: Design</td>
                <td class="course-credits">0.50</td>
                <td class="course-grades">B+</td><td class="course-grades">B</td><td></td><td></td>
                <td colspan="6"></td>
            </tr>
            <tr>
                <td>Fitness for Life</td>
                <td class="course-credits">0.50</td>
                <td></td><td></td><td></td><td class="course-grades">A</td>
                <td colspan="6"></td>
            </tr>
            <tr>
                <td>Lang Arts 10 Honors</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B+</td><td class="course-grades">B</td><td class="course-grades">B-</td><td class="course-grades">B+</td>
                <td colspan="6"></td>
            </tr>
            <tr>
                <td>Secondary Mathematics 2 Honors</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B</td><td class="course-grades">B-</td><td class="course-grades">C+</td><td class="course-grades">B</td>
                <td colspan="6"></td>
            </tr>
            <tr>
                <td>Spanish II</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">B</td><td class="course-grades">B-</td><td class="course-grades">B+</td><td class="course-grades">B</td>
                <td colspan="6"></td>
            </tr>
        </tbody>
    </table>

    <div class="gpa-summary">
        <span class="gpa-group">
            <span>09th Q1: 3.214</span> <span>Q2: 3.057</span> <span>Q3: 3.514</span> <span>Q4: 3.471</span> <span>Other: 3.186</span>
        </span>
        <span class="gpa-group">
            <span>11th Q1: 3.487</span> <span>Q2: 3.243</span> <span>Q3: 3.586</span> <span>Q4: 3.571</span> <span>Other:</span>
        </span>
        <br>
        <span class="gpa-group">
            <span>10th Q1: 2.962</span> <span>Q2: 3.629</span> <span>Q3: 3.157</span> <span>Q4: 3.514</span> <span>Other:</span>
        </span>
        <span class="gpa-group">
            <span>12th Q1: 3.115</span> <span>Q2: 3.487</span><span>Q3: 3.621</span><span>Q4: 3.258</span><span>Other:</span>
        </span>
    </div>

    <div class="footer-info">
        <p>All grades are based on a 4.000 scale. Credit and grades are issued quarterly with a typical course earning 0.25 units of credit per quarter or 1.00 credit per year. 28 credits, grades 9-12, are required for graduation.</p>
    </div>

    <div class="footer-grid">
        <div>
            <p style="margin:0;"><strong>West Anchorage High School</strong></p>
            <p style="margin:0;">1700 Hillcrest Drive</p>
            <p style="margin:0;">Anchorage, AK 99517</p>
        </div>
        <div class="center-text"></div> <div class="right-text"></div>
    </div>

    <div class="signature-line">
        <div>
            <span class="signature-text"><input type="text" class="editable-input" value="Darryl Jerde" data-field="signature" style="background:transparent;border:none;font-family:inherit;font-size:inherit;font-style:inherit;text-align:center;width:150px;"></span>
            Signature
        </div>
    </div>

    <div class="date-footer">
        <input type="text" class="editable-input" value="07/01/2025" data-field="issueDate" style="width:80px;">
    </div>
</div>

<!-- 引入html2canvas库用于截图 - 使用多个CDN备用 -->
<script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"
        onerror="this.onerror=null; this.src='https://unpkg.com/html2canvas@1.4.1/dist/html2canvas.min.js'"></script>

<script>
let editMode = false;

function toggleEdit() {
    editMode = !editMode;
    const inputs = document.querySelectorAll('.editable-input');
    const btn = document.querySelector('.edit-btn');

    if (editMode) {
        inputs.forEach(input => {
            input.style.background = '#f0f8ff';
            input.style.outline = '1px solid #007cba';
            input.disabled = false;
        });
        btn.textContent = '关闭编辑';
        btn.style.background = '#f44336';
    } else {
        inputs.forEach(input => {
            input.style.background = 'transparent';
            input.style.outline = 'none';
            input.disabled = true;
        });
        btn.textContent = '开启编辑';
        btn.style.background = '#4CAF50';
    }
}

function saveData() {
    const data = {};
    const inputs = document.querySelectorAll('.editable-input[data-field]');

    inputs.forEach(input => {
        data[input.dataset.field] = input.value;
    });

    localStorage.setItem('transcriptData', JSON.stringify(data));
    alert('数据已保存到浏览器本地存储！');
}

function loadData() {
    const savedData = localStorage.getItem('transcriptData');
    if (savedData) {
        const data = JSON.parse(savedData);
        const inputs = document.querySelectorAll('.editable-input[data-field]');

        inputs.forEach(input => {
            if (data[input.dataset.field]) {
                input.value = data[input.dataset.field];
            }
        });
        alert('数据已从本地存储加载！');
    } else {
        alert('没有找到保存的数据！');
    }
}

function saveAsImage() {
    // 检查html2canvas是否加载成功
    if (typeof html2canvas === 'undefined') {
        alert('图片保存功能正在加载中，请稍后再试！');
        return;
    }

    // 隐藏编辑控制按钮
    const editControls = document.querySelector('.edit-controls');
    editControls.style.display = 'none';

    // 临时关闭编辑模式以获得更好的截图效果
    const wasInEditMode = editMode;
    if (editMode) {
        toggleEdit();
    }

    // 获取学生姓名作为文件名
    const studentName = document.querySelector('[data-field="studentName"]').value || 'Student';
    const fileName = `${studentName}_成绩单_${new Date().toISOString().slice(0,10)}.png`;

    // 显示加载提示
    const loadingMsg = document.createElement('div');
    loadingMsg.innerHTML = '正在生成图片，请稍候...';
    loadingMsg.style.cssText = 'position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:#000;color:#fff;padding:20px;border-radius:5px;z-index:9999;';
    document.body.appendChild(loadingMsg);

    // 使用html2canvas截图
    html2canvas(document.querySelector('.a4-container'), {
        scale: 1.5, // 降低scale避免内存问题
        useCORS: true,
        allowTaint: false,
        backgroundColor: '#ffffff',
        logging: false,
        removeContainer: true,
        imageTimeout: 15000,
        onclone: function(clonedDoc) {
            // 在克隆的文档中移除编辑控制
            const clonedControls = clonedDoc.querySelector('.edit-controls');
            if (clonedControls) {
                clonedControls.remove();
            }
        }
    }).then(canvas => {
        try {
            // 创建下载链接
            const link = document.createElement('a');
            link.download = fileName;
            link.href = canvas.toDataURL('image/png', 0.9);

            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert('成绩单图片已保存！');
        } catch (downloadError) {
            console.error('下载失败:', downloadError);
            // 备用方案：在新窗口打开图片
            const newWindow = window.open();
            newWindow.document.write('<img src="' + canvas.toDataURL('image/png') + '" style="max-width:100%;"/>');
            alert('请在新窗口中右键保存图片！');
        }
    }).catch(error => {
        console.error('保存图片失败:', error);
        alert('保存图片失败！错误信息：' + error.message + '\n\n建议：\n1. 刷新页面重试\n2. 使用Chrome或Edge浏览器\n3. 检查网络连接');
    }).finally(() => {
        // 清理和恢复
        document.body.removeChild(loadingMsg);
        editControls.style.display = 'block';

        // 恢复之前的编辑模式
        if (wasInEditMode) {
            toggleEdit();
        }
    });
}

// 添加备用的打印功能
function printTranscript() {
    // 隐藏编辑控制按钮
    const editControls = document.querySelector('.edit-controls');
    editControls.style.display = 'none';

    // 临时关闭编辑模式
    const wasInEditMode = editMode;
    if (editMode) {
        toggleEdit();
    }

    // 打印
    window.print();

    // 恢复
    editControls.style.display = 'block';
    if (wasInEditMode) {
        toggleEdit();
    }
}

// 初始化：默认禁用编辑
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('.editable-input');
    inputs.forEach(input => {
        input.disabled = true;
    });
});
</script>

</body>
</html>